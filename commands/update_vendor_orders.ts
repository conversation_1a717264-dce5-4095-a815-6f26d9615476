import { BaseCommand } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'
import queue from "@rlanz/bull-queue/services/main";
import UpdatePackageTrackingHistoryJob from "#jobs/update_package_tracking_history_job";
import ZnOrderFulfillment from "#models/zn_order_fulfillment";

export default class UpdateVendorOrders extends BaseCommand {
  static commandName = 'update:orders'
  static description = ''

  static options: CommandOptions = {
    startApp: true
  }

  async run() {
    const orders = await ZnOrderFulfillment.query()
      .whereNotNull('trackingCompany')
      .whereNotNull('trackingNumber');

    for (const order of orders) {
      try {
        const trackingNumber = order?.trackingNumber
        if (!trackingNumber) continue

        // Update history and fields
        await queue.dispatch(UpdatePackageTrackingHistoryJob, {
          tracking: null,
          trackingNumber, trackingCompany: order?.trackingCompany, from: 'api' }, { queueName: 'tracking' })

      } catch (error) {
        console.error(`Failed to update tracking for order ID ${order.id}:`, error)
      }
    }
  }
}
