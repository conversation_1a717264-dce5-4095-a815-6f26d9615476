import ZnCart from '#models/zn_cart'
import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import { ACTION, RESOURCE } from '#constants/authorization'
import ZnCartSection from '#models/zn_cart_section'
import ZnCartItem from '#models/zn_cart_item'
import { AdminCartService } from '../../services/cart/admin_cart_service.js'
import {
  adminAddItemValidator,
  adminAddBundleValidator,
  adminUpdateQuantityValidator,
  adminUpdateSectionQuantityValidator,
  adminUpdateCartItemValidator,
  adminDeleteCartSectionValidator,
  cartFilterValidator,
} from '../../validators/cart/cart_validator.js'

export default class AdminCartController {
  private adminCartService: AdminCartService

  constructor() {
    this.adminCartService = new AdminCartService()
  }

  /**
   * @index
   * @tag Admin Cart
   * @summary List all carts with pagination and filtering
   * @queryParam page - Page number - @type(number) @default(1)
   * @queryParam limit - Items per page - @type(number) @default(10)
   * @queryParam search - Search by user email, name (first/last/full), or phone - @type(string)
   * @queryParam userId - Filter by specific user ID - @type(string)
   * @responseBody 200 - {"data": [<ZnCart>], "meta": {"total": 100, "lastPage": 10}}
   * @responseBody 401 - Unauthorized access
   */
  async index({ request, response, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.CART)

      const payload = await request.validateUsing(cartFilterValidator)
      const { page = 1, limit = 10, search, userId } = payload

      const query = ZnCart.query()
        .preload('user')
        .preload('cartSections', (sectionQuery) => {
          sectionQuery
            .preload('cartItems', (itemQuery) => {
              itemQuery.preload('product').preload('variant').preload('discount')
            })
            .preload('bundle')
        })
        .whereNull('deletedAt')

      if (search) {
        query.whereHas('user', (userQuery) => {
          userQuery
            .whereILike('email', `%${search}%`)
            .orWhereRaw('LOWER(`firstName`) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(`lastName`) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(CONCAT(`firstName`, " ", `lastName`)) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(CONCAT(`lastName`, " ", `firstName`)) LIKE LOWER(?)', [`%${search}%`])
            .orWhereILike('phone', `%${search}%`)
        })
      }

      if (userId) {
        query.where('userId', userId)
      }

      query.has('user')

      query.orderBy('updatedAt', 'desc')

      const result = await query.paginate(page, Math.min(limit, 100))

      const transformedData = result.all().map((cart) => {
        const cartData = cart.serialize()

        const itemCount = cart.cartSections.reduce((total: number, section: any) => {
          return total + parseInt(section.quantity || '0')
        }, 0)

        const totalValue = cart.cartSections.reduce(
          (total: number, section: any) => total + parseFloat(section.total || '0'),
          0
        )

        const lastActivity = cart.updatedAt

        return {
          ...cartData,
          itemCount,
          totalValue,
          lastActivity,
        }
      })

      return response.ok({
        data: transformedData,
        meta: result.toJSON().meta,
      })
    } catch (error) {
      logger.error('Failed to get carts', { error })
      return response.badRequest({
        success: false,
        message: 'Failed to get carts',
        error: error.message,
      })
    }
  }

  /**
   * @getUserCart
   * @tag Admin Cart
   * @summary Get or create user cart
   * @param userId - User ID
   * @responseBody 200 - Cart data with sections and items
   * @responseBody 400 - Bad request
   */
  async getUserCart({ params, response, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.CART)

      const { userId } = params

      let cart = await ZnCart.query()
        .where('userId', userId)
        .whereNull('deletedAt')
        .preload('user')
        .preload('cartSections', (sectionQuery) => {
          sectionQuery
            .whereNull('deletedAt')
            .preload('cartItems', (itemQuery) => {
              itemQuery
                .whereNull('deletedAt')
                .preload('product')
                .preload('variant')
                .preload('discount')
            })
            .preload('bundle')
        })
        .first()

      if (!cart) {
        cart = await ZnCart.create({ userId })
        await cart.load('user')
        await cart.load('cartSections')
      }

      const cartData = cart.serialize()

      const itemCount = cart.cartSections.reduce((total: number, section: any) => {
        return total + parseInt(section.quantity || '0')
      }, 0)

      const totalValue = cart.cartSections.reduce(
        (total: number, section: any) => total + parseFloat(section.total || '0'),
        0
      )

      return response.ok({
        ...cartData,
        itemCount,
        totalValue,
      })
    } catch (error) {
      logger.error('Failed to get user cart', { error, userId: params.userId })
      return response.badRequest({
        success: false,
        message: 'Failed to get user cart',
        error: error.message,
      })
    }
  }

  /**
   * @show
   * @tag Admin Cart
   * @summary Get specific cart details
   * @param id - Cart ID
   * @responseBody 200 - Cart data with sections and items
   * @responseBody 404 - Cart not found
   */
  async show({ params, response, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.CART)

      const { id } = params

      const cart = await ZnCart.query()
        .where('id', id)
        .whereNull('deletedAt')
        .preload('user')
        .preload('cartSections', (sectionQuery) => {
          sectionQuery
            .preload('cartItems', (itemQuery) => {
              itemQuery.preload('product').preload('variant').preload('discount')
            })
            .preload('bundle')
        })
        .first()

      if (!cart) {
        return response.notFound({
          success: false,
          message: 'Cart not found',
        })
      }

      const cartData = cart.serialize()

      const itemCount = cart.cartSections.reduce((total: number, section: any) => {
        return total + parseInt(section.quantity || '0')
      }, 0)

      const totalValue = cart.cartSections.reduce(
        (total: number, section: any) => total + parseFloat(section.total || '0'),
        0
      )

      return response.ok({
        ...cartData,
        itemCount,
        totalValue,
      })
    } catch (error) {
      logger.error('Failed to get cart', { error, cartId: params.id })
      return response.badRequest({
        success: false,
        message: 'Failed to get cart',
        error: error.message,
      })
    }
  }

  /**
   * @destroy
   * @tag Admin Cart
   * @summary Delete entire cart
   * @param id - Cart ID
   * @responseBody 200 - Success message
   * @responseBody 404 - Cart not found
   */
  async destroy({ params, response, auth, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.CART)

      const { id } = params
      const adminId = (auth.user as any)?.id

      if (!adminId) {
        return response.unauthorized({
          success: false,
          message: 'Authentication required',
        })
      }

      const cart = await ZnCart.query()
        .where('id', id)
        .whereNull('deletedAt')
        .preload('cartSections', (sectionQuery) => {
          sectionQuery.whereNull('deletedAt').preload('cartItems', (itemQuery) => {
            itemQuery.whereNull('deletedAt')
          })
        })
        .first()

      if (!cart) {
        return response.notFound({
          success: false,
          message: 'Cart not found or already deleted',
        })
      }

      const cartSectionIds = cart.cartSections.map((section) => section.id)

      if (cartSectionIds.length > 0) {
        await ZnCartItem.query().whereIn('cartSectionId', cartSectionIds).delete()
      }

      if (cartSectionIds.length > 0) {
        await ZnCartSection.query().whereIn('id', cartSectionIds).delete()
      }

      await cart.delete()

      logger.info('Cart deleted by admin', { adminId, cartId: id })

      return response.ok({ message: 'Cart deleted successfully' })
    } catch (error) {
      logger.error('Failed to delete cart', { error, cartId: params.id })
      return response.badRequest({
        success: false,
        message: 'Failed to delete cart',
        error: error.message,
      })
    }
  }

  /**
   * @addItemToCart
   * @tag Admin Cart
   * @summary Add product to cart
   * @param cartId - Cart ID
   * @responseBody 201 - Item added successfully
   * @responseBody 400 - Bad request
   */
  async addItemToCart({ params, request, response, auth, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.CART)

      const { cartId } = params
      const payload = await request.validateUsing(adminAddItemValidator)
      const adminId = (auth.user as any)?.id

      if (!adminId) {
        return response.unauthorized({
          success: false,
          message: 'Authentication required',
        })
      }

      const result = await this.adminCartService.addItemToCart({
        ...payload,
        cartId: cartId!,
      })

      await result.load('cartItems', (query: any) => {
        query.preload('discount').preload('variant').preload('product')
      })
      await result.load('bundle')

      logger.info('Item added to cart by admin', { adminId, cartId, variantId: payload.variantId })

      return response.created({
        success: true,
        data: result,
        message: 'Item added to cart successfully',
      })
    } catch (error) {
      logger.error('Failed to add item to cart', { error, cartId: params.cartId })
      return response.badRequest({
        success: false,
        message: 'Failed to add item to cart',
        error: error.message,
      })
    }
  }

  /**
   * @addBundleToCart
   * @tag Admin Cart
   * @summary Add bundle to cart
   * @param cartId - Cart ID
   * @responseBody 201 - Bundle added successfully
   * @responseBody 400 - Bad request
   */
  async addBundleToCart({ params, request, response, auth, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.CART)

      const { cartId } = params
      const payload = await request.validateUsing(adminAddBundleValidator)
      const adminId = (auth.user as any)?.id

      if (!adminId) {
        return response.unauthorized({
          success: false,
          message: 'Authentication required',
        })
      }

      const result = await this.adminCartService.addBundleToCart({
        ...payload,
        cartId: cartId!,
      })

      await result.load('cartItems', (query: any) => {
        query.preload('discount').preload('variant').preload('product')
      })
      await result.load('bundle')

      logger.info('Bundle added to cart by admin', { adminId, cartId, bundleId: payload.bundleId })

      return response.created({
        success: true,
        data: result,
        message: 'Bundle added to cart successfully',
      })
    } catch (error) {
      logger.error('Failed to add bundle to cart', { error, cartId: params.cartId })
      return response.badRequest({
        success: false,
        message: 'Failed to add bundle to cart',
        error: error.message,
      })
    }
  }

  /**
   * @updateSectionQuantity
   * @tag Admin Cart
   * @summary Update section quantity
   * @param cartSectionId - Cart section ID
   * @responseBody 200 - Section updated successfully
   * @responseBody 400 - Bad request
   */
  async updateSectionQuantity({ params, request, response, auth, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.CART)

      const { cartSectionId } = params
      const payload = await request.validateUsing(adminUpdateQuantityValidator)
      const adminId = (auth.user as any)?.id

      if (!adminId) {
        return response.unauthorized({
          success: false,
          message: 'Authentication required',
        })
      }

      const result = await this.adminCartService.updateSectionQuantity({
        cartSectionId,
        ...payload,
      })

      if (result) {
        await result.load('cartItems', (query: any) => {
          query.preload('discount').preload('variant').preload('product')
        })
        await result.load('bundle')
      }

      logger.info('Section quantity updated by admin', {
        adminId,
        cartSectionId,
        quantity: payload.quantity,
      })

      return response.ok({
        success: true,
        data: result,
        message: result ? 'Section quantity updated successfully' : 'Section removed successfully',
      })
    } catch (error) {
      logger.error('Failed to update section quantity', {
        error,
        cartSectionId: params.cartSectionId,
      })
      return response.badRequest({
        success: false,
        message: 'Failed to update section quantity',
        error: error.message,
      })
    }
  }

  /**
   * @updateSectionQuantityAdmin
   * @tag Admin Cart
   * @summary Update section quantity using app-side logic (exact match)
   * @responseBody 200 - Section updated successfully
   * @responseBody 400 - Bad request
   * @responseBody 404 - Cart section not found
   */
  async updateSectionQuantityAdmin({ request, response, auth, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.CART)

      const payload = await request.validateUsing(adminUpdateSectionQuantityValidator)
      const adminId = (auth.user as any)?.id

      if (!adminId) {
        return response.unauthorized({
          success: false,
          message: 'Authentication required',
        })
      }

      const result = await this.adminCartService.updateSectionQuantityAdmin(payload)

      logger.info('Section quantity updated by admin (app-side logic)', {
        adminId,
        cartSectionId: payload.cartSectionId,
        quantity: payload.quantity,
      })

      return response.ok(result)
    } catch (error) {
      logger.error('Failed to update section quantity (app-side logic)', {
        error,
      })

      if (error.message === 'Cart section not found') {
        return response.notFound({ message: 'Cart section not found' })
      }

      return response.badRequest({
        success: false,
        message: 'Failed to update section quantity',
        error: error.message,
      })
    }
  }

  /**
   * @updateCartItem
   * @tag Admin Cart
   * @summary Update individual cart item
   * @param cartItemId - Cart item ID
   * @responseBody 200 - Item updated successfully
   * @responseBody 400 - Bad request
   */
  async updateCartItem({ params, request, response, auth, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.CART)

      const { cartItemId } = params
      const payload = await request.validateUsing(adminUpdateCartItemValidator)
      const adminId = (auth.user as any)?.id

      if (!adminId) {
        return response.unauthorized({
          success: false,
          message: 'Authentication required',
        })
      }

      const result = await this.adminCartService.updateCartItem({
        cartItemId,
        price: payload.price,
        notes: payload.notes,
      })

      await result?.load('product')
      await result?.load('variant')
      await result?.load('discount')

      logger.info('Cart item updated by admin', { adminId, cartItemId, updates: payload })

      return response.ok({
        success: true,
        data: result,
        message: 'Cart item updated successfully',
      })
    } catch (error) {
      logger.error('Failed to update cart item', { error, cartItemId: params.cartItemId })
      return response.badRequest({
        success: false,
        message: 'Failed to update cart item',
        error: error.message,
      })
    }
  }

  /**
   * @updateCartItemQuantity
   * @tag Admin Cart
   * @summary Update cart item quantity by updating cart section
   * @param cartItemId - Cart item ID
   * @responseBody 200 - Quantity updated successfully
   * @responseBody 400 - Bad request
   */
  async updateCartItemQuantity({ params, request, response, auth, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.CART)

      const { cartItemId } = params
      const { quantity, notes } = request.all()
      const adminId = (auth.user as any)?.id

      if (!adminId) {
        return response.unauthorized({
          success: false,
          message: 'Authentication required',
        })
      }

      if (quantity === undefined || typeof quantity !== 'number' || quantity < 0) {
        return response.badRequest({
          success: false,
          message: 'Valid quantity is required',
        })
      }

      const result = await this.adminCartService.updateCartItemQuantity(cartItemId, quantity, notes)

      if (result) {
        await result.load('cartItems', (query: any) => {
          query.preload('discount').preload('variant').preload('product')
        })
        await result.load('bundle')
      }

      return response.ok(result)
    } catch (error) {
      return response.badRequest({
        success: false,
        message: 'Failed to update cart item quantity',
        error: error.message,
      })
    }
  }

  /**
   * @deleteCartSection
   * @tag Admin Cart
   * @summary Delete cart section
   * @param cartSectionId - Cart section ID
   * @responseBody 200 - Section deleted successfully
   * @responseBody 400 - Bad request
   */
  async deleteCartSection({ params, request, response, auth, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.CART)

      const { cartSectionId } = params
      const payload = await request.validateUsing(adminDeleteCartSectionValidator)
      const adminId = (auth.user as any)?.id

      if (!adminId) {
        return response.unauthorized({
          success: false,
          message: 'Authentication required',
        })
      }

      await this.adminCartService.deleteCartSection(cartSectionId, payload.notes)

      logger.info('Cart section deleted by admin', { adminId, cartSectionId, notes: payload.notes })

      return response.ok({
        success: true,
        message: 'Cart section deleted successfully',
      })
    } catch (error) {
      logger.error('Failed to delete cart section', { error, cartSectionId: params.cartSectionId })
      return response.badRequest({
        success: false,
        message: 'Failed to delete cart section',
        error: error.message,
      })
    }
  }
}
