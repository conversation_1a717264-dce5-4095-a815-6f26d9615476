import { inject } from '@adonisjs/core'
import type { HttpContext } from '@adonisjs/core/http'
import ZnStoreService from '#models/store_service/zn_store_service'
import ZnStorePackage from '#models/store_service/zn_store_package'
import ZnStoreServiceCategory from '#models/store_service/zn_store_service_category'
import { createServiceValidator, updateServiceValidator } from '#validators/store-service/index'
import db from '@adonisjs/lucid/services/db'

@inject()
export default class StoreServiceController {
  /**
   * @swagger
   * /api/v1/app/store-service/services:
   *   get:
   *     tags:
   *       - Store Service
   *     summary: List services grouped by category
   *     parameters:
   *       - name: storeId
   *         in: query
   *         required: true
   *         schema:
   *           type: string
   *       - name: page
   *         in: query
   *         schema:
   *           type: number
   *       - name: limit
   *         in: query
   *         schema:
   *           type: number
   *       - name: categories
   *         in: query
   *         schema:
   *           type: array
   *           items:
   *             type: string
   *       - name: priceFrom
   *         in: query
   *         schema:
   *           type: number
   *       - name: priceTo
   *         in: query
   *         schema:
   *           type: number
   *       - name: durationFrom
   *         in: query
   *         schema:
   *           type: number
   *       - name: durationTo
   *         in: query
   *         schema:
   *           type: number
   */
  async index({ request, response }: HttpContext) {
    const qs = await request.qs()
    const parseNumber = (v: any) => {
      const n = Number(v)
      return Number.isFinite(n) ? n : undefined
    }
    const page = Number(qs.page ?? 1)
    const limit = Number(qs.limit ?? 10)
    const search = qs.search
    const storeId = qs.storeId
    // Normalize categories to array of strings
    const categories: string[] = Array.isArray(qs.categories)
      ? (qs.categories as string[])
      : qs.categories
      ? [qs.categories]
      : []
    // Coerce numeric filters even if provided as strings
    const priceFrom = parseNumber(qs.priceFrom)
    const priceTo = parseNumber(qs.priceTo)
    const durationFrom = parseNumber(qs.durationFrom)
    const durationTo = parseNumber(qs.durationTo)

    let query = ZnStoreService.query().preload('categories').preload('image').preload('store')
    if (storeId) {
      query.where('storeId', storeId)
    }

    if (search) {
      query.whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
    }

    if (categories.length) {
      query.whereHas('categories', (query) => {
        query.whereIn('id', categories)
      })
    }

    if (priceFrom !== undefined) {
      query.where('price', '>=', Number(priceFrom))
    }

    if (priceTo !== undefined) {
      query.where('price', '<=', Number(priceTo))
    }

    if (durationFrom !== undefined) {
      query.where('duration', '>=', Number(durationFrom))
    }

    if (durationTo !== undefined) {
      query.where('duration', '<=', Number(durationTo))
    }

    const result = await query.paginate(page, limit)

    return response.ok(result.serialize())
  }

  /**
   * @swagger
   * /api/v1/app/store-service/services:
   *   post:
   *     tags:
   *       - Store Service
   *     summary: Create a new service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *               - price
   *               - duration
   *               - storeId
   *               - categories
   *             properties:
   *               name:
   *                 type: string
   *               price:
   *                 type: number
   *               duration:
   *                 type: number
   *               storeId:
   *                 type: string
   *               imageId:
   *                 type: string
   *               categories:
   *                 type: array
   *                 items:
   *                   type: string
   */
  async store({ request, response }: HttpContext) {
    const payload = await createServiceValidator.validate(request.all())

    const serviceData: any = { ...payload }
    if (serviceData.imageId === null || serviceData.imageId === '') {
      delete serviceData.imageId
    }

    const service = await ZnStoreService.create(serviceData)

    if (payload.categories?.length && payload.categories.length > 0) {
      await service.related('categories').attach(payload.categories)
    }

    const loadPromises = [service.load('categories'), service.load('store')]

    // Only load image if imageId is not null
    if (service.imageId) {
      loadPromises.push(service.load('image'))
    }

    await Promise.all(loadPromises)

    return response.ok(service.serialize())
  }

  /**
   * @swagger
   * /api/v1/app/store-service/services/{id}:
   *   put:
   *     tags:
   *       - Store Service
   *     summary: Update a service
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: string
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *               price:
   *                 type: number
   *               duration:
   *                 type: number
   *               imageId:
   *                 type: string
   *               categories:
   *                 type: array
   *                 items:
   *                   type: string
   */
  async update({ request, response, params }: HttpContext) {
    const payload = await updateServiceValidator.validate(request.all())
    const service = await ZnStoreService.findOrFail(params.id)

    const updateData: any = { ...payload }
    if (updateData.imageId === null || updateData.imageId === '') {
      delete updateData.imageId
    }

    await service.merge(updateData).save()

    if (payload.categories?.length && payload.categories.length > 0) {
      await service.related('categories').sync(payload.categories)
    }

    const loadPromises = [service.load('categories'), service.load('store')]

    // Only load image if imageId is not null
    if (service.imageId) {
      loadPromises.push(service.load('image'))
    }

    await Promise.all(loadPromises)

    return response.ok(service.serialize())
  }

  /**
   * @swagger
   * /api/v1/app/store-service/services/{id}:
   *   delete:
   *     tags:
   *       - Store Service
   *     summary: Delete a service
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: string
   */
  async destroy({ params, response }: HttpContext) {
    const service = await ZnStoreService.findOrFail(params.id)
    await service.softDelete()
    return response.ok({
      message: 'Service deleted successfully',
    })
  }

  /**
   * @swagger
   * /api/v1/app/store-service/combined:
   *   get:
   *     tags:
   *       - Store Service Combined
   *     summary: Get packages and categories combined
   *     parameters:
   *       - name: storeId
   *         in: query
   *         required: true
   *         schema:
   *           type: string
   *       - name: page
   *         in: query
   *         schema:
   *           type: number
   *       - name: limit
   *         in: query
   *         schema:
   *           type: number
   *       - name: search
   *         in: query
   *         schema:
   *           type: string
   *       - name: categories
   *         in: query
   *         schema:
   *           type: array
   *           items:
   *             type: string
   *       - name: priceFrom
   *         in: query
   *         schema:
   *           type: number
   *       - name: priceTo
   *         in: query
   *         schema:
   *           type: number
   *       - name: durationFrom
   *         in: query
   *         schema:
   *           type: number
   *       - name: durationTo
   *         in: query
   *         schema:
   *           type: number
   */
  async combined({ request, response }: HttpContext) {
    const qs = await request.qs()
    const parseNumber = (v: any) => {
      const n = Number(v)
      return Number.isFinite(n) ? n : undefined
    }
    const page = Number(qs.page ?? 1)
    const limit = Number(qs.limit ?? 10)
    const search = qs.search
    const storeId = qs.storeId
    const categories: string[] = Array.isArray(qs.categories)
      ? (qs.categories as string[])
      : qs.categories
      ? [qs.categories]
      : []
    const priceFrom = parseNumber(qs.priceFrom)
    const priceTo = parseNumber(qs.priceTo)
    const durationFrom = parseNumber(qs.durationFrom)
    const durationTo = parseNumber(qs.durationTo)

    if (!storeId) {
      return response.badRequest({ error: 'storeId is required' })
    }

    let packagesQuery = ZnStorePackage.query()
      .where('storeId', storeId)
      .preload('image')
      .preload('services', (query) => {
        query.preload('image')
        query.pivotColumns(['customPrice'])
      })

    if (search) {
      packagesQuery.where((query) => {
        query
          .whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
          .orWhereHas('services', (serviceQuery) => {
            serviceQuery.whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
          })
      })
    }

    if (categories.length) {
      packagesQuery.whereHas('services', (query) => {
        query.whereHas('categories', (query) => {
          query.whereIn('id', categories)
        })
      })
    }

    if (priceFrom !== undefined || priceTo !== undefined) {
      packagesQuery.whereExists((subQuery) => {
        subQuery
          .from('zn_store_package_services as ps')
          .join('zn_store_services as s', 's.id', 'ps.serviceId')
          .whereRaw('ps.packageId = zn_store_packages.id')
          .groupBy('ps.packageId', 'ps.customPrice', 'ps.serviceId')
          .having(db.raw('SUM(COALESCE(ps.customPrice, s.price))'), '>=', Number(priceFrom || 0))
          .if(priceTo !== undefined, (query) => {
            query.having(db.raw('SUM(COALESCE(ps.customPrice, s.price))'), '<=', Number(priceTo!))
          })
      })
    }

    if (durationFrom !== undefined || durationTo !== undefined) {
      packagesQuery.whereExists((subQuery) => {
        subQuery
          .from('zn_store_package_services as ps')
          .join('zn_store_services as s', 's.id', 'ps.serviceId')
          .whereRaw('ps.packageId = zn_store_packages.id')
          .groupBy('ps.packageId', 'ps.customPrice', 'ps.serviceId')
          .having(db.raw('SUM(s.duration)'), '>=', Number(durationFrom || 0))
          .if(durationTo !== undefined, (query) => {
            query.having(db.raw('SUM(s.duration)'), '<=', Number(durationTo!))
          })
      })
    }

    let categoriesQuery = ZnStoreServiceCategory.query()
      .where('storeId', storeId)
      .preload('services', (query) => {
        query.preload('image')
      })

    if (search) {
      categoriesQuery.where((query) => {
        query
          .whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
          .orWhereHas('services', (serviceQuery) => {
            serviceQuery.whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
          })
      })
    }

    if (categories.length) {
      categoriesQuery.whereIn('id', categories)
    }

    const [packagesData, categoriesData] = await Promise.all([
      packagesQuery.exec(),
      categoriesQuery.exec(),
    ])

    const [processedPackages, processedCategories] = await Promise.all([
      (async () => {
        if (packagesData.length === 0) return []

        return packagesData.map((package_: any) => {
          const serializedPackage = package_.serialize()

          // Process services with custom prices from pivot data
          const processedServices = serializedPackage.services.map((service: any) => {
            // Use custom price from pivot if available, otherwise use original service price
            const customPrice = service.$extras?.pivot_customPrice
            if (customPrice !== null && customPrice !== undefined) {
              service.price = customPrice
            }

            return service
          })

          return {
            ...serializedPackage,
            services: processedServices,
            isPackage: true,
          }
        })
      })(),
      Promise.resolve(
        categoriesData.map((category: any) => ({
          ...category.serialize(),
          isPackage: false,
        }))
      ),
    ])

    const combinedData = [...processedPackages, ...processedCategories].sort((a, b) => {
      if (a.isPackage && !b.isPackage) return -1
      if (!a.isPackage && b.isPackage) return 1
      return 0
    })

    const total = combinedData.length
    const perPage = limit
    const currentPage = page
    const lastPage = Math.ceil(total / perPage)
    const offset = (currentPage - 1) * perPage
    const paginatedData = combinedData.slice(offset, offset + perPage)

    const meta = {
      total,
      perPage: perPage,
      currentPage: currentPage,
      lastPage: lastPage,
      firstPage: 1,
      firstPageUrl: `?page=1&limit=${perPage}`,
      lastPageUrl: `?page=${lastPage}&limit=${perPage}`,
      nextPageUrl: currentPage < lastPage ? `?page=${currentPage + 1}&limit=${perPage}` : null,
      previousPageUrl: currentPage > 1 ? `?page=${currentPage - 1}&limit=${perPage}` : null,
    }

    return response.ok({
      data: paginatedData,
      meta,
    })
  }
}
