import { inject } from '@adonisjs/core'
import type { HttpContext } from '@adonisjs/core/http'
import ZnStorePackage from '#models/store_service/zn_store_package'
import ZnStoreService from '#models/store_service/zn_store_service'
import { createPackageValidator, updatePackageValidator } from '#validators/store-service/index'
import db from '@adonisjs/lucid/services/db'

@inject()
export default class StorePackageController {
  /**
   * @swagger
   * /api/v1/app/store-service/packages:
   *   get:
   *     tags:
   *       - Store Packages
   *     summary: List packages
   *     parameters:
   *       - name: storeId
   *         in: query
   *         required: true
   *         schema:
   *           type: string
   *       - name: page
   *         in: query
   *         schema:
   *           type: number
   *       - name: limit
   *         in: query
   *         schema:
   *           type: number
   *       - name: search
   *         in: query
   *         schema:
   *           type: string
   *         description: Search packages by name only
   *       - name: priceFrom
   *         in: query
   *         schema:
   *           type: number
   *         description: Minimum total price filter for package
   *       - name: priceTo
   *         in: query
   *         schema:
   *           type: number
   *         description: Maximum total price filter for package
   *       - name: durationFrom
   *         in: query
   *         schema:
   *           type: number
   *         description: Minimum total duration filter for package (in minutes)
   *       - name: durationTo
   *         in: query
   *         schema:
   *           type: number
   *         description: Maximum total duration filter for package (in minutes)
   *     responses:
   *       200:
   *         description: List of packages with total price and duration
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 data:
   *                   type: array
   *                   items:
   *                     type: object
   *                     properties:
   *                       id:
   *                         type: string
   *                       name:
   *                         type: string
   *                       storeId:
   *                         type: string
   *                       totalPrice:
   *                         type: number
   *                         description: Total price of all services in the package
   *                       totalDuration:
   *                         type: number
   *                         description: Total duration of all services in the package (in minutes)
   *                       services:
   *                         type: array
   *                         items:
   *                           type: object
   *                           properties:
   *                             id:
   *                               type: string
   *                             name:
   *                               type: string
   *                             price:
   *                               type: number
   *                             duration:
   *                               type: number
   */
  async index({ request, response }: HttpContext) {
    const qs = request.qs()
    const parseNumber = (v: any) => {
      const n = Number(v)
      return Number.isFinite(n) ? n : undefined
    }
    const page = Number(qs.page ?? 1)
    const limit = Number(qs.limit ?? 10)
    const storeId = qs.storeId
    const search = qs.search
    const priceFrom = parseNumber(qs.priceFrom)
    const priceTo = parseNumber(qs.priceTo)
    const durationFrom = parseNumber(qs.durationFrom)
    const durationTo = parseNumber(qs.durationTo)

    let query = ZnStorePackage.query()
      .where('storeId', storeId)
      .orderBy('createdAt', 'desc')
      .preload('image')
      .preload('services', (query) => {
        query.preload('image').orderBy('createdAt', 'desc')
      })

    if (search) {
      query.whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
    }

    interface CustomPriceMap {
      [packageId: string]: {
        [serviceId: string]: number
      }
    }

    const result = (await query.paginate(page, limit)).serialize()

    const packageIds = result.data.map((d) => d.id)
    const customPricesQuery = await db
      .from('zn_store_package_services')
      .whereIn('packageId', packageIds)
      .select('packageId', 'serviceId', 'customPrice')

    const customPricesMap: CustomPriceMap = {}
    customPricesQuery.forEach((row: any) => {
      if (!customPricesMap[row.packageId]) {
        customPricesMap[row.packageId] = {}
      }
      customPricesMap[row.packageId][row.serviceId] = row.customPrice
    })

    const filteredPackages = await Promise.all(
      result.data.map(async (package_: any) => {
        let totalPrice = 0
        let totalDuration = 0

        package_.services = await Promise.all(
          package_.services.map(async (service: any) => {
            const customPrice = customPricesMap[package_.id]?.[service.id]
            const finalPrice = customPrice || service.price

            totalPrice += Number(finalPrice)
            totalDuration += Number(service.duration)

            return {
              ...service,
              price: finalPrice,
            }
          })
        )

        package_.totalPrice = totalPrice
        package_.totalDuration = totalDuration

        return package_
      })
    )

    const finalFilteredPackages = filteredPackages.filter((package_: any) => {
      if (priceFrom !== undefined && package_.totalPrice < Number(priceFrom)) {
        return false
      }
      if (priceTo !== undefined && package_.totalPrice > Number(priceTo)) {
        return false
      }

      if (durationFrom !== undefined && package_.totalDuration < Number(durationFrom)) {
        return false
      }
      if (durationTo !== undefined && package_.totalDuration > Number(durationTo)) {
        return false
      }

      return true
    })

    const filteredResult = {
      ...result,
      data: finalFilteredPackages,
      meta: {
        ...result.meta,
        total: finalFilteredPackages.length,
        lastPage: Math.ceil(finalFilteredPackages.length / result.meta.perPage),
      },
    }

    return response.ok(filteredResult)
  }

  /**
   * @swagger
   * /api/v1/app/store-service/packages:
   *   post:
   *     tags:
   *       - Store Packages
   *     summary: Create a new package
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *               - storeId
   *               - services
   *             properties:
   *               name:
   *                 type: string
   *               storeId:
   *                 type: string
   *               imageId:
   *                 type: string
   *               services:
   *                 type: array
   *                 items:
   *                   type: object
   *                   required:
   *                     - id
   *                   properties:
   *                     id:
   *                       type: string
   *                     customPrice:
   *                       type: number
   */
  async store({ request, response }: HttpContext) {
    const payload = await createPackageValidator.validate(request.all())

    const packageData: any = {
      name: payload.name,
      storeId: payload.storeId,
    }
    if (payload.imageId && payload.imageId !== null && payload.imageId !== '') {
      packageData.imageId = payload.imageId
    }

    const package_ = await ZnStorePackage.create(packageData)

    // Prepare pivot data with custom prices
    const pivotData = await Promise.all(
      payload.services.map(async (service) => {
        const originalService = await ZnStoreService.findOrFail(service.id)
        return {
          [service.id]: {
            customPrice: service.customPrice || originalService.price,
          },
        }
      })
    )

    // Attach services with custom prices
    await package_.related('services').attach(Object.assign({}, ...pivotData))

    const loadPromises = [
      package_.load((loader) => {
        loader.load('services', (query) => {
          query.preload('image')
          query.pivotColumns(['customPrice'])
        })
      }),
    ]

    if (package_.imageId) {
      loadPromises.push(package_.load('image'))
    }

    await Promise.all(loadPromises)

    return response.ok(package_.serialize())
  }

  /**
   * @swagger
   * /api/v1/app/store-service/packages/{id}:
   *   put:
   *     tags:
   *       - Store Packages
   *     summary: Update a package
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: string
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *               imageId:
   *                 type: string
   *               services:
   *                 type: array
   *                 items:
   *                   type: object
   *                   required:
   *                     - id
   *                   properties:
   *                     id:
   *                       type: string
   *                     customPrice:
   *                       type: number
   */
  async update({ request, response, params }: HttpContext) {
    const payload = await updatePackageValidator.validate(request.all())
    const package_ = await ZnStorePackage.findOrFail(params.id)

    const updateData: any = {}
    if (payload.name) {
      updateData.name = payload.name
    }
    if (payload.imageId && payload.imageId !== null && payload.imageId !== '') {
      updateData.imageId = payload.imageId
    } else if (payload.imageId === null || payload.imageId === '') {
      updateData.imageId = undefined
    }

    if (Object.keys(updateData).length > 0) {
      await package_.merge(updateData).save()
    }

    if (payload.services) {
      // Prepare pivot data with custom prices
      const pivotData = await Promise.all(
        payload.services.map(async (service) => {
          const originalService = await ZnStoreService.findOrFail(service.id)
          return {
            [service.id]: {
              customPrice: service.customPrice || originalService.price,
            },
          }
        })
      )

      // Sync services with custom prices
      await package_.related('services').sync(Object.assign({}, ...pivotData))
    }

    const loadPromises = [
      package_.load((loader) => {
        loader.load('services', (query) => {
          query.preload('image')
          query.pivotColumns(['customPrice'])
        })
      }),
    ]

    if (package_.imageId) {
      loadPromises.push(package_.load('image'))
    }

    await Promise.all(loadPromises)

    return response.ok(package_.serialize())
  }

  /**
   * @swagger
   * /api/v1/app/store-service/packages/{id}:
   *   delete:
   *     tags:
   *       - Store Packages
   *     summary: Delete a package
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: string
   */
  async destroy({ params, response }: HttpContext) {
    const package_ = await ZnStorePackage.findOrFail(params.id)
    await package_.softDelete()
    return response.ok({
      message: 'Package deleted successfully',
    })
  }
}
