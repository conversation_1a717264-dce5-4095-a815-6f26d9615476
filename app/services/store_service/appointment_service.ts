import { inject } from '@adonisjs/core'
import ZnAppointment, { EAppointmentStatus } from '#models/store_service/zn_appointment'
import ZnStoreService from '#models/store_service/zn_store_service'
import ZnStorePackage from '#models/store_service/zn_store_package'
import ZnStoreTax from '#models/store_service/zn_store_tax'
import ZnStore from '#models/zn_store'
import { DateTime } from 'luxon'
import { v4 as uuidv4 } from 'uuid'

@inject()
export default class AppointmentService {
  async create(data: any) {
    const {
      storeId,
      customerId,
      startTime,
      endTime,
      notes,
      taxId,
      services = [],
      packages = [],
    } = data

    // Normalize start/end time to Date instances and store as UTC
    const normalizedStart = startTime
      ? DateTime.fromJSDate(startTime instanceof Date ? startTime : new Date(startTime)).toUTC()
      : null
    const normalizedEnd = endTime
      ? DateTime.fromJSDate(endTime instanceof Date ? endTime : new Date(endTime)).toUTC()
      : null

    const appointment = await ZnAppointment.create({
      storeId,
      customerId,
      status: EAppointmentStatus.BOOKED,
      startTime: normalizedStart,
      endTime: normalizedEnd,
      notes,
      taxId,
      subtotal: 0,
      discount: 0,
      taxAmount: 0,
      tipAmount: 0,
      total: 0,
    })

    if (services.length > 0) {
      const storeServices = await ZnStoreService.query()
        .whereIn('id', services)
        .where('storeId', storeId)

      const servicesPivot: Record<string, { id: string; price: number; duration: number }> = {}
      for (const service of storeServices) {
        servicesPivot[service.id] = {
          id: uuidv4(),
          price: service.price,
          duration: service.duration,
        }
      }
      if (Object.keys(servicesPivot).length > 0) {
        await appointment.related('services').sync(servicesPivot)
      }
    }

    if (packages.length > 0) {
      // Allow payload to be either array of ids or array of objects { id, price }
      const packageIds = packages.map((p: any) => (typeof p === 'string' ? p : p.id))

      const storePackages = await ZnStorePackage.query()
        .whereIn('id', packageIds)
        .where('storeId', storeId)

      const packagePivot: Record<string, { id: string; price: number }> = {}
      for (const pkg of storePackages) {
        const inputPackage = (packages as any[]).find((p: any) => (typeof p === 'string' ? p : p.id) === pkg.id)
        const packagePrice = inputPackage && typeof inputPackage === 'object' && inputPackage.price
          ? Number(inputPackage.price)
          : 0
        packagePivot[pkg.id] = {
          id: uuidv4(),
          price: packagePrice,
        }
      }
      if (Object.keys(packagePivot).length > 0) {
        await appointment.related('packages').sync(packagePivot)
      }
    }

    await this.calculateTotals(appointment.id)

    return this.findById(appointment.id)
  }

  async findById(id: string) {
    const appointment = await ZnAppointment.query()
      .where('id', id)
      .preload('store')
      .preload('customer', (q) => {
        q.preload('avatarMedia')
      })
      .preload('tax')
      .preload('services')
      .preload('packages', (query) => {
        query.preload('services')
      })
      .firstOrFail()

    return appointment
  }

  async getByStore(storeId: string, params: any = {}) {
    const { page = 1, limit = 10, status, startDate, endDate, customerId } = params

    const query = ZnAppointment.query()
      .where('storeId', storeId)
      .preload('customer', (q) => {
        q.preload('avatarMedia')
      })
      .preload('services')
      .preload('packages', (query) => {
        query.preload('services')
      })

    if (status) {
      query.where('status', status)
    }

    if (startDate && endDate) {
      query
        .where(
          'startTime',
          '>=',
          startDate
            ? (DateTime.fromJSDate(new Date(startDate)).toUTC().startOf('day').toJSDate() as any)
            : undefined
        )
        .where(
          'endTime',
          '<=',
          endDate
            ? (DateTime.fromJSDate(new Date(endDate)).toUTC().endOf('day').toJSDate() as any)
            : undefined
        )
    }

    if (customerId) {
      query.where('customerId', customerId)
    }

    const result = await query.orderBy('startTime', 'desc').paginate(page, limit)

    return result
  }

  async getByCustomer(customerId: string, params: any = {}) {
    const { page = 1, limit = 10, status, storeId } = params

    const query = ZnAppointment.query()
      .where('customerId', customerId)
      .preload('store')
      .preload('services')
      .preload('packages', (query) => {
        query.preload('services')
      })
      .preload('customer', (q) => {
        q.preload('avatarMedia')
      })

    if (status) {
      query.where('status', status)
    }

    if (storeId) {
      query.where('storeId', storeId)
    }

    const result = await query.orderBy('startTime', 'desc').paginate(page, limit)

    return result
  }

  async update(id: string, data: any) {
    const appointment = await this.findById(id)
    const { storeId, customerId, status, startTime, endTime, notes, taxId, services, packages } =
      data

    if (storeId) appointment.storeId = storeId
    if (customerId) appointment.customerId = customerId
    if (status) appointment.status = status
    if (startTime) {
      const start = startTime instanceof Date ? startTime : new Date(startTime)
      appointment.startTime = DateTime.fromJSDate(start).toUTC()
    }
    if (endTime) {
      const end = endTime instanceof Date ? endTime : new Date(endTime)
      appointment.endTime = DateTime.fromJSDate(end).toUTC()
    }
    if (notes !== undefined) appointment.notes = notes
    if (taxId !== undefined) appointment.taxId = taxId

    await appointment.save()

    if (services) {
      if (services.length > 0) {
        const storeServices = await ZnStoreService.query()
          .whereIn('id', services)
          .where('storeId', appointment.storeId)

        const servicesPivot: Record<string, { id: string; price: number; duration: number }> = {}
        for (const service of storeServices) {
          servicesPivot[service.id] = {
            id: uuidv4(),
            price: service.price,
            duration: service.duration,
          }
        }
        if (Object.keys(servicesPivot).length > 0) {
          await appointment.related('services').sync(servicesPivot)
        }
      } else {
        // empty array means clear
        await appointment.related('services').sync({})
      }
    }

    if (packages) {
      if (packages.length > 0) {
        const packageIds = packages.map((p: any) => (typeof p === 'string' ? p : p.id))
        const storePackages = await ZnStorePackage.query()
          .whereIn('id', packageIds)
          .where('storeId', appointment.storeId)

        const packagePivot: Record<string, { id: string; price: number }> = {}
        for (const pkg of storePackages) {
          const inputPackage = (packages as any[]).find((p: any) => (typeof p === 'string' ? p : p.id) === pkg.id)
          const packagePrice = inputPackage && typeof inputPackage === 'object' && inputPackage.price
            ? Number(inputPackage.price)
            : 0
          packagePivot[pkg.id] = {
            id: uuidv4(),
            price: packagePrice,
          }
        }
        if (Object.keys(packagePivot).length > 0) {
          await appointment.related('packages').sync(packagePivot)
        }
      } else {
        // empty array means clear
        await appointment.related('packages').sync({})
      }
    }

    if (services || packages || taxId !== undefined) {
      await this.calculateTotals(id)
    }

    return this.findById(id)
  }

  async updateStatus(id: string, status: EAppointmentStatus) {
    const appointment = await this.findById(id)
    appointment.status = status
    await appointment.save()
    return appointment
  }

  async delete(id: string) {
    const appointment = await this.findById(id)
    await appointment.delete()
    return { success: true }
  }

  async calculateTotals(id: string) {
    const appointment = await this.findById(id)
    let subtotal = 0

    const services = await appointment.related('services').query()
    for (const service of services) {
      const servicePrice =
        typeof service.$extras.pivot_price === 'number'
          ? service.$extras.pivot_price
          : parseFloat(service.$extras.pivot_price || '0')
      subtotal += servicePrice || 0
    }

    const packages = await appointment.related('packages').query()
    for (const pkg of packages) {
      const packagePrice =
        typeof pkg.$extras.pivot_price === 'number'
          ? pkg.$extras.pivot_price
          : parseFloat(pkg.$extras.pivot_price || '0')
      subtotal += packagePrice || 0
    }

    let taxAmount = 0
    if (appointment.taxId) {
      const tax = await ZnStoreTax.find(appointment.taxId as string)
      if (tax) {
        taxAmount = (subtotal * tax.value) / 100
      }
    }

    const roundedSubtotal = parseFloat(subtotal.toFixed(2))
    const roundedTaxAmount = parseFloat(taxAmount.toFixed(2))
    const roundedTotal = parseFloat((roundedSubtotal + roundedTaxAmount).toFixed(2))

    appointment.subtotal = roundedSubtotal
    appointment.taxAmount = roundedTaxAmount
    appointment.total = roundedTotal

    await appointment.save()
    return appointment
  }

  async getNextAvailableDay({
    storeId,
    serviceIds = [],
    packageIds = [],
    fromDate,
  }: {
    storeId: string
    serviceIds?: string[]
    packageIds?: string[]
    fromDate?: string
  }): Promise<string | null> {
    const store = await ZnStore.findOrFail(storeId)
    const workingHoursRaw = store.workingHour
    const timezone = store.timezone || 'UTC'
    let workingHours: any[] = []
    try {
      workingHours =
        typeof workingHoursRaw === 'string' ? JSON.parse(workingHoursRaw) : workingHoursRaw
    } catch {
      workingHours = []
    }

    let totalDuration = 0
    if (serviceIds.length > 0) {
      const services = await ZnStoreService.query().whereIn('id', serviceIds)
      totalDuration += services.reduce((sum, s) => sum + (s.duration || 0), 0)
    }
    if (packageIds.length > 0) {
      for (const packageId of packageIds) {
        const pkg = await ZnStorePackage.findOrFail(packageId)
        await pkg.load('services')
        totalDuration += pkg.services.reduce((sum: number, s: any) => sum + (s.duration || 0), 0)
      }
    }
    // If no services or packages selected, use default duration of 60 minutes
    if (totalDuration === 0) {
      totalDuration = 60
    }

    let searchDate = fromDate
      ? DateTime.fromISO(fromDate, { zone: timezone })
      : DateTime.now().setZone(timezone)
    searchDate = searchDate.startOf('day')
    const maxDays = 30
    for (let i = 0; i < maxDays; i++) {
      const dayName = searchDate.toFormat('cccc')
      const wh = workingHours.find((h: any) => h.name?.toLowerCase() === dayName.toLowerCase())
      if (!wh || !wh.isOpen) {
        searchDate = searchDate.plus({ days: 1 })
        continue
      }
      const open = DateTime.fromFormat(wh.from, 'HH:mm', { zone: timezone })
      const close = DateTime.fromFormat(wh.to, 'HH:mm', { zone: timezone })
      if (!open.isValid || !close.isValid) {
        searchDate = searchDate.plus({ days: 1 })
        continue
      }
      const dayStart = searchDate.set({
        hour: open.hour,
        minute: open.minute,
        second: 0,
        millisecond: 0,
      })
      const dayEnd = searchDate.set({
        hour: close.hour,
        minute: close.minute,
        second: 0,
        millisecond: 0,
      })
      const appointments = await ZnAppointment.query()
        .where('storeId', storeId)
        .whereIn('status', ['booked', 'confirmed', 'checked-in', 'in-service', 'completed'])
        .where('startTime', '>=', dayStart.toUTC().toISO() || '')
        .where('endTime', '<=', dayEnd.toUTC().toISO() || '')
        .orderBy('startTime', 'asc')

      let slotStart = dayStart
      let slotEnd = slotStart.plus({ minutes: totalDuration })
      let found = false
      while (slotEnd <= dayEnd) {
        const overlap = appointments.some((appt: any) => {
          const apptStart = DateTime.fromJSDate(appt.startTime, { zone: timezone })
          const apptEnd = DateTime.fromJSDate(appt.endTime, { zone: timezone })
          return apptEnd > slotStart && apptStart < slotEnd
        })
        if (!overlap) {
          found = true
          break
        }
        slotStart = slotStart.plus({ minutes: 15 })
        slotEnd = slotStart.plus({ minutes: totalDuration })
      }
      if (found) {
        return searchDate.toISODate()
      }
      searchDate = searchDate.plus({ days: 1 })
    }
    return null
  }

  async getAvailableSlots({
    storeId,
    serviceIds = [],
    packageIds = [],
    date,
  }: {
    storeId: string
    serviceIds?: string[]
    packageIds?: string[]
    date: string
  }): Promise<{ startTime: string; endTime: string }[]> {
    const store = await ZnStore.findOrFail(storeId)
    const workingHoursRaw = store.workingHour
    const timezone = store.timezone || 'UTC'
    let workingHours: any[] = []
    try {
      workingHours =
        typeof workingHoursRaw === 'string' ? JSON.parse(workingHoursRaw) : workingHoursRaw
    } catch {
      workingHours = []
    }

    let totalDuration = 0
    if (serviceIds.length > 0) {
      const services = await ZnStoreService.query().whereIn('id', serviceIds)
      totalDuration += services.reduce((sum, s) => sum + (s.duration || 0), 0)
    }
    if (packageIds.length > 0) {
      for (const packageId of packageIds) {
        const pkg = await ZnStorePackage.findOrFail(packageId)
        await pkg.load('services')
        totalDuration += pkg.services.reduce((sum: number, s: any) => sum + (s.duration || 0), 0)
      }
    }
    // If no services or packages selected, use default duration of 60 minutes
    if (totalDuration === 0) {
      totalDuration = 60
    }

    const searchDate = DateTime.fromISO(date, { zone: timezone }).startOf('day')
    if (!searchDate.isValid) return []

    const dayName = searchDate.toFormat('cccc') // e.g., 'Monday'

    const wh = workingHours.find((h: any) => h.name?.toLowerCase() === dayName.toLowerCase())
    if (!wh || !wh.isOpen) {
      return []
    }

    const open = DateTime.fromFormat(wh.from, 'HH:mm', { zone: timezone })
    const close = DateTime.fromFormat(wh.to, 'HH:mm', { zone: timezone })
    if (!open.isValid || !close.isValid) {
      return []
    }

    const dayStart = searchDate.set({
      hour: open.hour,
      minute: open.minute,
      second: 0,
      millisecond: 0,
    })
    const dayEnd = searchDate.set({
      hour: close.hour,
      minute: close.minute,
      second: 0,
      millisecond: 0,
    })

    const appointments = await ZnAppointment.query()
      .where('storeId', storeId)
      .whereIn('status', ['booked', 'confirmed', 'checked-in', 'in-service', 'completed'])
      .where('startTime', '>=', dayStart.toUTC().toISO() || '')
      .where('startTime', '<', dayEnd.toUTC().toISO() || '')
      .orderBy('startTime', 'asc')

    const availableSlots: { startTime: string; endTime: string }[] = []
    let slotStart = dayStart
    let slotEnd = slotStart.plus({ minutes: totalDuration })

    while (slotEnd <= dayEnd) {
      const overlap = appointments.some((appt: any) => {
        const apptStart = DateTime.fromJSDate(appt.startTime).setZone(timezone)
        const apptEnd = DateTime.fromJSDate(appt.endTime).setZone(timezone)
        return apptEnd > slotStart && apptStart < slotEnd
      })

      if (!overlap) {
        availableSlots.push({
          startTime: slotStart.toISO() || '',
          endTime: slotEnd.toISO() || '',
        })
      }

      slotStart = slotStart.plus({ minutes: 15 })
      slotEnd = slotStart.plus({ minutes: totalDuration })
    }

    return availableSlots
  }
}
