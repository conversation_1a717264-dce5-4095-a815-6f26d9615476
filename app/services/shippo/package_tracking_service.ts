import { ShippoService } from "./index.js";
import { Shippo, TracksRequest } from "shippo";
import ZnOrderFulfillment from "#models/zn_order_fulfillment";
import ZnPackageTrackingHistory from "#models/zn_package_tracking_history";
import { DateTime } from "luxon";
import ZnVendorOrder, { EVendorOrderStatus } from "#models/zn_vendor_order";
import queue from "@rlanz/bull-queue/services/main";
import PackingTrackingRegisterWebhookJob from "#jobs/package_tracking_register_webhook_job";
import ZnOrder from "#models/zn_order";
import {EOrderStatus} from "#constants/order";
import {ShopifyNotificationService} from "../shopify/webhooks/shopify_notification_service.js";

export class PackageTrackingService {
  private shippoService: ShippoService
  private notificationService: ShopifyNotificationService
  private shippo: Shippo
  carrierMap: Record<string, string>
  codeToNameMap: Record<string, string>
  trackingToOrderStatusMap: Record<string, string>


  constructor() {
    this.shippoService = new ShippoService()
    this.shippo = this.shippoService.getShippo()
    this.notificationService = new ShopifyNotificationService()

    this.carrierMap = {
      "Airterra": "airterra",
      "APC Postal": "apc_postal",
      "APG": "apg",
      "Aramex": "aramex",
      "Asendia": "asendia_us",
      "Australia Post": "australia_post",
      "Better Trucks": "better_trucks",
      "BorderGuru": "borderguru",
      "Boxberry": "boxberry",
      "Bring": "bring",
      "Canada Post": "canada_post",
      "CDL": "cdl",
      "Chronopost": "chronopost",
      "Colissimo": "colissimo",
      "Collect+": "collect_plus",
      "Correos BR": "correios_br",
      "Correos Spain": "correos_espana",
      "Deutsche Post": "deutsche_post",
      "DHL Benelux": "dhl_benelux",
      "DHL eCommerce": "dhl_ecommerce",
      "DHL Express": "dhl_express",
      "DHL Germany": "dhl_germany",
      "DPD DE": "dpd_de",
      "DPD UK": "dpd_uk",
      "ePostGlobal": "rr_donnelley",
      "Estafeta": "estafeta",
      "Evri UK": "hermes_uk",
      "Fastway Australia": "fastway_australia",
      "FedEx": "fedex",
      "Globegistics": "globegistics",
      "GLS US": "gls_us",
      "GSO": "gso",
      "Jitsu (formerly Axlehire)": "axlehire",
      "La Poste": "la_poste",
      "Lasership": "lasership",
      "LSO": "lso",
      "Mondial Relay": "mondial_relay",
      "Nippon Express": "nippon_express",
      "NZ Post": "new_zealand_post",
      "OnTrac": "ontrac",
      "Passport": "passport",
      "PCF": "pcf",
      "Pitney Bowes": "pitney_bowes",
      "Poste Italiane": "poste_italiane",
      "Posti": "posti",
      "Purolator": "purolator",
      "Royal Mail Intersoft": "royal_mail",
      "Royal Mail SF": "royal_mail_sf",
      "Russian Post": "russian_post",
      "Sendle": "sendle",
      "SkyPostal": "skypostal",
      "Stuart": "stuart",
      "UDS": "uds",
      "UPS": "ups",
      "USPS": "usps",
      "Veho": "veho"
    }

    // Reverse map: code => name
    this.codeToNameMap = Object.entries(this.carrierMap).reduce((acc, [name, code]) => {
      acc[code] = name
      return acc
    }, {} as Record<string, string>)

    this.trackingToOrderStatusMap = {
      "UNKNOWN": "pending",
      "WAITING": "readyToShip",
      "TRANSIT": "inTransit",
      "PRE_TRANSIT": "inTransit",
      "DELIVERED": "delivered",
      "RETURNED": "rto",
      "FAILURE": "held",
      "AVAILABLE_FOR_PICKUP": "held",
      "EXCEPTION": "held",
      "CANCELLED": "cancelled"
    }
  }


  getCarrierCode(carrierName: string) {
    return this.carrierMap[carrierName] || 'shippo'
  }

  getCarrierName(code: string): string | null {
    return this.codeToNameMap[code] || 'shippo'
  }

  async registerWebhookAsync(data: any) {
    await queue.dispatch(
      PackingTrackingRegisterWebhookJob,
      { data },
      { attempts: 1 }
    )
  }

  registerWebhook(data: any) {
    const trackingRequest: TracksRequest = {
      carrier: this.getCarrierCode(data.carrier),
      metadata: data.metadata,
      trackingNumber: data.trackingNumber
    }
    return this.shippo.trackingStatus.create(trackingRequest)
  }

  getOrderStatusFromTracking(trackingStatus: string): string {
    const normalized = trackingStatus?.toUpperCase()
    return this.trackingToOrderStatusMap[normalized] || "pending"
  }

  getTrackings(trackingCompany: string, trackingNumber: string) {
    return this.shippo.trackingStatus.get(
      trackingNumber,
      this.getCarrierCode(trackingCompany)
    )
  }

  async updateTrackingsWebhook(payload: any) {
    const trackingData = payload.data

    //Update fulfilment
    const orderFulfilment = await ZnOrderFulfillment.findBy({
      trackingCompany: this.getCarrierName(trackingData.carrier),
      trackingNumber: trackingData.tracking_number
    })

    if (orderFulfilment) {
      orderFulfilment.eta = DateTime.fromISO(trackingData.eta).toFormat('yyyy-MM-dd HH:mm:ss') as any
      orderFulfilment.originalEta = DateTime.fromISO(trackingData.original_eta).toFormat('yyyy-MM-dd HH:mm:ss') as any
      orderFulfilment.serviceLevel = trackingData.service_level
      orderFulfilment.status = trackingData?.tracking_status.status
      await orderFulfilment.save()

      //Update order status
      await this.updateOrderStatus(orderFulfilment, trackingData?.tracking_status.status)

      //Send notification
      const order = await ZnOrder.find(orderFulfilment.orderId)
      if(order) {
        await order.load('user')
        await this.notificationService.fulfilNotification(order, orderFulfilment);
      }

      for (const history of trackingData.tracking_history) {
        await ZnPackageTrackingHistory.updateOrCreate({ objectId: history.object_id }, {
          //Update history
          trackingNumber: trackingData.tracking_number,
          carrier: trackingData.carrier,
          objectCreated: history.object_created,
          objectId: history.object_id,
          status: history.status,
          statusDetails: history.status_details,
          statusDate: DateTime.fromISO(history.status_date).toFormat('yyyy-MM-dd HH:mm:ss') as any,
          city: history.location?.city || null,
          state: history.location?.state || null,
          zip: history.location?.zip || null,
          country: history.location?.country || null,
          fulfilId: orderFulfilment.id,
        })
      }
    }
  }

  async updateTrackingsFromApi(trackingData: any) {
    const trackingNumber = trackingData.trackingNumber
    const carrier = trackingData.carrier

    if (!trackingNumber || !carrier) return

    const orderFulfilment = await ZnOrderFulfillment.findBy({
      trackingNumber,
      trackingCompany: carrier,
    })

    if (!orderFulfilment) return

    // Update basic shipment info
    orderFulfilment.eta = trackingData.eta
      ? DateTime.fromJSDate(new Date(trackingData.eta)).toFormat('yyyy-MM-dd HH:mm:ss') as any
      : null

    orderFulfilment.originalEta = trackingData.originalEta
      ? DateTime.fromJSDate(new Date(trackingData.originalEta)).toFormat('yyyy-MM-dd HH:mm:ss') as any
      : null

    orderFulfilment.serviceLevel = trackingData.servicelevel?.name || null
    orderFulfilment.trackingUrl = trackingData.trackingUrl || null
    orderFulfilment.status = trackingData?.trackingStatus.status

    await orderFulfilment.save()

    //Update order status
    await this.updateOrderStatus(orderFulfilment, trackingData?.trackingStatus.status)

    // Save tracking history
    for (const history of trackingData.trackingHistory || []) {
      await ZnPackageTrackingHistory.updateOrCreate(
        { objectId: history.objectId },
        {
          trackingNumber,
          carrier,
          objectCreated: history.objectCreated,
          objectId: history.objectId,
          status: history.status,
          statusDetails: history.statusDetails,
          statusDate: history.statusDate
            ? DateTime.fromJSDate(new Date(history.statusDate)).toFormat('yyyy-MM-dd HH:mm:ss') as any
            : null,
          city: history.location?.city || null,
          state: history.location?.state || null,
          zip: history.location?.zip || null,
          country: history.location?.country || null,
          fulfilId: orderFulfilment.id,
        }
      )
    }
  }

  async updateOrderStatus(orderFulfilment: ZnOrderFulfillment, trackingStatus: string) {
    // Update vendor order status
    const vendorOrder = await ZnVendorOrder.findBy({fulfillmentId: orderFulfilment.id})
    if (vendorOrder) {
      const mappedStatus = this.getOrderStatusFromTracking(trackingStatus)
      vendorOrder.status = mappedStatus as EVendorOrderStatus
      await vendorOrder.save()
    }

    //Update main order status
    const order = await ZnOrder.find(orderFulfilment.orderId)
    if(order) {
      await order.load('fulfillments')
      order.status = this.getOrderStatusFromFulfillments(order.fulfillments)
      await order.save()
    }
  }

  getOrderStatusFromFulfillments(fulfillments: ZnOrderFulfillment[]): EOrderStatus {
    const statuses = fulfillments.map(f => f.status?.toUpperCase())

    const all = (s: string[]) => statuses.every(x => s.includes(x))
    const some = (s: string) => statuses.includes(s)

    if (statuses.length === 0) return EOrderStatus.Processing

    if (all(['UNKNOWN', 'WAITING', 'READYTOSHIP'])) return EOrderStatus.Processing
    if (all(['DELIVERED'])) return EOrderStatus.Completed
    if (some('DELIVERED') && !all(['DELIVERED'])) return EOrderStatus.Shipped
    if (some('TRANSIT')) return EOrderStatus.Ongoing
    if (all(['CANCELLED', 'RTO'])) return EOrderStatus.Cancelled

    return EOrderStatus.Processing
  }
}
